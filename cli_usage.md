# 用户飞书查询CLI工具使用说明

## 快速开始

本工具允许你通过命令行测试用户飞书查询功能。

### 1. 列出所有可用用户
```bash
uv run python test_user_cli.py list
```

### 2. 搜索特定用户
```bash
uv run python test_user_cli.py search "张"
```

### 3. 执行用户查询测试

#### 基础用法
```bash
uv run python test_user_cli.py test --user "张三" --query "杭州市昨天销售额"
```

#### 指定agent
```bash
uv run python test_user_cli.py test --user "李四" --query "M1战队销售额" --agent sales_kpi_analytics
```

#### 使用邮箱地址
```bash
uv run python test_user_cli.py test --user "<EMAIL>" --query "库存分析"
```

## 支持的Agent类型

- `sales_order_analytics` (默认) - 销售订单分析
- `sales_kpi_analytics` - 销售KPI分析  
- `warehouse_and_fulfillment` - 仓储履约分析
- `general_chat_bot` - 通用聊天机器人

## 测试流程说明

当执行查询测试时，工具会：
1. 根据用户姓名在数据库中查找open_id
2. 生成测试消息ID和会话ID
3. 调用FeishuQueryProcessor处理查询
4. 获取最终结果并保存报告到本地

## 输出文件

成功执行后会在当前目录生成：
```
user_test_YYYYMMDD_HHMMSS.md
```

包含完整的测试报告、查询结果和时间统计。

## 调试示例

```python
# 查看用户是否存在
UserQueryService.get_user_info_by_username("张三")

# 搜索用户
UserQueryService.search_users("张", limit=5)

# 列出前10个用户
UserQueryService.list_all_users(10)
```

## 常见问题

1. **用户不存在**: 确保输入的用户名在user表中
2. **权限问题**: 确保有数据库读取权限
3. **查询失败**: 检查agent响应和数据库连接