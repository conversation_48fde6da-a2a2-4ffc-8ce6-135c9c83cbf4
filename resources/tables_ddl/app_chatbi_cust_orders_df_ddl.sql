-- 【注意】此表是ODPS表，仅可使用ODPS SQL来获取数据，无法使用MySQL来查询。
-- 需要使用工具：fetch_odps_sql_result 来查询。
-- 使用时，必须要加上过滤条件ds=max_pt('app_chatbi_cust_orders_df') 来获取最新数据，其他ds都是非法的，不加ds条件会报错。
-- 【注意】用户的查询时间跨度在3个月以上，使用此表才有意义，否则直接使用MySQL表会更快。
CREATE TABLE IF NOT EXISTS app_chatbi_cust_orders_df
(
    order_no                 STRING COMMENT '订单号'
    ,order_item_id           STRING COMMENT '订单明细ID'
    ,order_type              BIGINT COMMENT '订单类型：0-普通（一次购买一次性配送完），1-省心送（一次购买可分多次配送），2-运费，3-代下单，10-虚拟商品（特指奶油黄金卡），30-‘顺鹿达’订单(鲜果POP)'
    ,order_status            BIGINT COMMENT '订单状态, 枚举值：1-待支付，2-待配送，3-待收货，6-已收货，7-申请退款订单，8-已退款订单，9-支付失败订单，10-支付中断超时关闭订单，11-已撤销订单，14-手动关闭订单，15-人工退款中; 若用户无特别说明查询异常状态订单, 则默认查询状态in (2, 3, 6)的订单, 这些为正常状态'
    ,order_item_status       BIGINT COMMENT '订单明细状态，同order_status'
    ,order_date              STRING COMMENT '订单日期，比如：20220811'
    ,order_time              STRING COMMENT '订单时间，比如：2022-08-11 16:42:11'
    ,pay_date                STRING COMMENT '订单支付日期，比如：20220811'
    ,confirm_date            STRING COMMENT '订单确认收货的日期, 比如：20220814'
    ,spu_id                  STRING COMMENT '商品SPU；每个SPU可能有多个sku。有的用户可能会提到pd_id，也是一样的'
    ,sku_id                  STRING COMMENT 'SKU编码，比如：N001S01R005'
    ,spu_name                STRING COMMENT '商品名称。比如‘安佳淡奶油’,‘国产红心火龙果’，‘嘉利宝白巧克力28%’等'
    ,sku_disc                STRING COMMENT 'SKU规格。比如：‘1*24包’、’1L*12盒‘、‘毛重6-8斤/一级’'
    ,cust_id                 STRING COMMENT '客户ID'
    ,cust_name               STRING COMMENT '客户名称/门店名称，比如：霸王茶姬石犀里店，旺山知味恋歌等'
    ,cust_type               STRING COMMENT '客户类型, 枚举值[面包蛋糕,茶饮,咖啡,其他,甜品冰淇淋,西餐]'
    ,admin_id                STRING COMMENT '大客户ID,'
    ,admin_full_name         STRING COMMENT '大客户的工商主体名字，很少用，比如鲜沐科技有限公司等'
    ,admin_name              STRING COMMENT '大客户名称，比如霸王茶姬、浙江茶百道等,'
    ,account_id              STRING COMMENT '下单的账户ID,'
    ,product_type            BIGINT COMMENT '产品类型'
    ,sku_cnt                 BIGINT COMMENT 'SKU购买件数'
    ,gift_cnt                BIGINT COMMENT '赠品件数'
    ,real_unit_amt           DECIMAL(18,2) COMMENT 'SKU实际单价(优惠后)'
    ,real_total_amt          DECIMAL(18,2) COMMENT 'SKU实际总价(优惠前)'
    ,origin_unit_amt         DECIMAL(18,2) COMMENT 'SKU原始单价(优惠前)'
    ,origin_total_amt        DECIMAL(18,2) COMMENT 'SKU原始总价(优惠后)'
    ,delivery_fee_amt        DECIMAL(18,2) COMMENT 'SKU分摊的配送费金额'
    ,real_total_gmv_amt      DECIMAL(18,2) COMMENT 'SKU+运费的实际GMV总金额'
    ,delivery_fee_coupon_amt DECIMAL(18,2) COMMENT '配送费优惠券金额'
    ,category_type           STRING COMMENT '类目类型，枚举值[鲜果、乳制品、非乳制品、其他]'
    ,category1               STRING COMMENT '一级类目名字'
    ,category2               STRING COMMENT '二级类目名字'
    ,category3               STRING COMMENT '三级类目,比如：桃、搅打型稀奶油、纯牛奶等'
    ,is_self_owned_brand     BIGINT COMMENT '是否自有品牌，自有品牌也叫做‘PB品(private brand)’，相反的，如果不是自有品牌，则叫‘NB’品(national brand)，1:PB品，0:NB品'
    ,sub_type                BIGINT COMMENT 'SKU子类型，枚举：1:代销不入仓(俗称‘全品类’)、2:代销入仓(俗称‘全品类’)、3:自营(经销)(我司自行采购、入库、销售)、5:顺鹿达商品(鲜果POP)'
    ,sku_brand               STRING COMMENT 'SKU品牌名字，比如：安佳、C味、Protag、蒙牛等'
    ,cust_register_province  STRING COMMENT '客户注册省份，如：浙江、广东、上海'
    ,cust_register_city      STRING COMMENT '客户注册城市，如：杭州市，广州市，上海市'
    ,cust_register_area      STRING COMMENT '客户注册行政区县(也有可能是县级市)，如：浦东'
    ,cust_register_time      STRING COMMENT '客户注册时间, 比如:2022-06-29 21:46:14'
    ,cust_register_date      STRING COMMENT '客户注册日期, 比如:20220629'
    ,cust_operate_status     BIGINT COMMENT '商户经营状态。0:正常, 1:倒闭, 2:待提交核验, 3:核验中, 4:已核验拒绝(补充资料后可再次发起核验)'
    ,cust_business_line      BIGINT COMMENT '客户业务线，0=鲜沐，1=顺鹿达(POP)'
    ,area_no                 BIGINT COMMENT '运营区域编号，比如1001'
    ,area_name               STRING COMMENT '运营区域名称，比如杭州、宁波、苏州'
    ,large_area_name         STRING COMMENT '运营大区名称，比如杭州大区(可能覆盖了多个杭州周边的城市)，苏南大区(覆盖了苏州等数个城市)'
    ,large_area_no           STRING COMMENT '运营大区编号'
    ,bd_name                 STRING COMMENT 'BD姓名,'
    ,bd_id                   BIGINT COMMENT 'BD ID/M2的团队数据'
    ,cust_phone              STRING COMMENT '客户电话'
    ,cust_first_order_date   STRING COMMENT '客户首次订单日期, 可以用于判断下单时，用户是否首次下单。比如：20250101'
    ,cust_first_sku_order_date STRING COMMENT '客户首次购买本SKU的日期，比如：20240909'
    ,warehouse_no              STRING COMMENT '此订单的SKU的履约库存仓号'
    ,warehouse_name            STRING COMMENT '此订单的此SKU的履约库存仓名字，比如嘉兴总仓、南京总仓'
    ,sku_cost_amt              DECIMAL(18,2) COMMENT 'SKU的总成本，可用于计算本单的履约毛利率'
    ,delivery_date_list        STRING COMMENT '此SKU的履约日期列表，通常而言只有省心送订单才会有多个履约日，如：20250101,20250122,20250103'
    ,first_deliver_date        STRING COMMENT '此SKU的首次配送日，通常而言只有省心送订单才会有多个履约日，如20250101'
    ,last_deliver_date         STRING COMMENT '此SKU的最近一次配送日，通常而言只有省心送订单才会有多个履约日，如20250121'
)
COMMENT '订单分析明细表，包含了近3年以来的所有订单数据，已经打平到了sku, cust_id, order_date, bd_id, warehouse_name 等维度，可以直接用于分析订单成本、履约仓库维度分析、运营大区分析、BD维度分析等'
PARTITIONED BY 
(
    ds                       STRING COMMENT '分区日期'
)
LIFECYCLE 7
;