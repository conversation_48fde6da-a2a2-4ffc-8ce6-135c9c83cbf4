import hashlib
import logging
import requests
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from requests.auth import HTT<PERSON><PERSON>asic<PERSON>uth
from src.services.agent.tools.tool_manager import tool_manager
from src.db.query import execute_sql_query
from src.utils.logger import logger
from src.models.query_result import SQLQueryResult

token_cache = {}
category_cache = {}
category_type_cache = {}


def init_category_cache():
    """初始化类目缓存"""
    if category_cache:  # 如果已经初始化过，直接返回
        return

    try:
        # 初始化的过程中，执行以下SQL来获取所有的类目
        sql = """
            SELECT
            a.id as category_id,
            concat(b.`category`, '_', a.`category`) category_path,
            a.type
            FROM
            `category` a
            INNER JOIN `category` b ON `a`.`parent_id` = b.`id`
            WHERE
            a.outdated = 0
            and a.parent_id is not null
            having
            category_path not like '%测试%';
        """
        result: SQLQueryResult = execute_sql_query(sql)
        if result.success:
            for item in result.data:
                category_cache[item[0]] = item[1]
                category_type_cache[item[0]] = item[2]
        else:
            logger.error(f"获取类目失败: {result.error}")
    except Exception as e:
        logger.error(f"初始化类目缓存失败: {e}")
        # 设置默认值，避免后续错误
        category_cache["0"] = "其他"
        category_type_cache["0"] = "其他"


def get_md5_encoded_string(phone_number, date, word):
    input_string = f"{phone_number}{date}{word}"
    input_bytes = input_string.encode("utf-8")
    md5_hash = hashlib.md5(input_bytes)
    md5_hex = md5_hash.hexdigest()
    return md5_hex


def get_token_for_phone(phone_number: str = "18618107293") -> str:
    today = datetime.now().strftime("%Y%m%d")

    cache_key = f"{phone_number}{today}"
    if cache_key in token_cache:
        logging.info(f"token_cache.get(cache_key):{token_cache.get(cache_key)}")
        return token_cache.get(cache_key)

    word = "login"
    md5_encoded_string = get_md5_encoded_string(phone_number, today, word)
    url = f"https://h5.summerfarm.net/openid?phone={phone_number}&sign={md5_encoded_string}"
    token = requests.get(url=url, timeout=2000, proxies={}).json()
    try:
        token_cache[cache_key] = token["data"]["token"]
        return token_cache.get(cache_key)
    except Exception as e:
        logging.error(f"获取token失败:{e}")
        raise e


def search_product_by_name(
    product_name: str,
    is_shunluda: bool = False,
) -> List[Dict[str, Any]]:
    """
    从API搜索商品，返回匹配的商品信息列表，支持搜索顺鹿达商品。
    【非常重要】如果搜索结果中有2个以上SKU，请你一定要和用户确认，以`{pd_name}, {sku}, {weight}`的形式确认(比如: 安佳淡奶油, N001S01R005, 1L*12盒)是哪一个具体SKU(规格)，避免错误。
    【非常重要】当使用本工具查询到多个结果时，禁止让用户进行确认哪个商品，思考下，过滤结果来继续下一步；
    【非常重要】如果搜索结果中没有命中，则可以使用文本匹配(like '%product_name%')的方式从数据库的pd_name字段中进行匹配。

    Args:
        product_name: 搜索关键词，通常是商品名称，比如'安佳淡奶油'或'保温袋'。
        is_shunluda: 是否是顺鹿达商品。

    Returns:
        List[Dict[str, Any]]: 匹配的商品信息列表，每个商品包含以下字段:
            - pd_name (str): 商品名称。例如: '安佳淡奶油'
            - weight (str): 商品规格。例如: '1L*12盒'
            - sku (str): 商品SKU编码。例如: 'N001S01R005'
            - category_type (int): 商品分类类型原始值 (例如 1: 其他, 2: 乳制品, 3: 非乳制品, 4: 水果)。例如: 2
            - category_type_description (str): 商品分类类型描述 (例如 '其他', '乳制品', '非乳制品', '水果')。例如: '乳制品'
            - brand (str): 商品品牌（如果有的话）。例如: '安佳'
            - sub_type (str): 商品子类型原始值以及描述 (例如 1: 代销, 2: 代销, 3: 鲜沐自营, 4: 代仓, 5: 顺鹿达)。
    """

    # 初始化类目缓存
    init_category_cache()

    url = "https://h5.summerfarm.net/search/product/1/20"
    headers = {"token": get_token_for_phone()}

    params = {
        "pdName": product_name,
    }

    if is_shunluda:
        params["areaNo"] = 44240

    logger.info(f"search_product_by_name: {params}")

    try:
        # 发送 POST 请求
        response = requests.get(url, headers=headers, params=params)
        # 检查响应状态码是否成功
        response.raise_for_status()

        # 解析响应为 JSON
        logger.info(f"search_product_by_name response: {response.text[0:100]}...")
        result = response.json()
        products = []

        # 检查响应数据结构，确保存在商品列表
        if "data" in result and "list" in result["data"]:
            # 遍历商品列表
            for item in result["data"]["list"]:
                # 获取分类类型值，默认为1 (其他)
                cate_type_value = item.get("cateType", 1)
                category_id = f'{item.get("categoryId", 0)}'
                # 根据分类类型值确定描述
                category_description = "其他"  # 默认描述为其他
                if cate_type_value == 2:
                    category_description = "乳制品"
                elif cate_type_value == 3:
                    category_description = "非乳制品"
                elif cate_type_value == 4:
                    category_description = "水果"

                sub_type = item.get("subType", 1)
                if sub_type == 5:
                    sub_type = f"{sub_type}:顺鹿达商品"
                elif sub_type == 3:
                    sub_type = f"{sub_type}:鲜沐自营商品"
                elif sub_type in (1, 2):
                    sub_type = f"{sub_type}:代销商品"
                else:
                    sub_type = f"{sub_type}:代仓商品"

                # 提取基础商品信息
                product_info = {
                    "pd_name": item.get("pdName", ""),
                    "weight": item.get("weight", ""),
                    "sku": item.get("sku", ""),
                    "category_type_description": category_description,  # 使用映射后的描述
                    "category_id": category_id,
                    "category_path": category_cache.get(category_id, "其他"),
                    "brand": "",  # 初始化品牌字段
                    "sub_type": sub_type,  # 使用映射后的子类型
                }

                # 查找品牌信息
                if "keyValueList" in item and isinstance(item["keyValueList"], list):
                    for kv_item in item["keyValueList"]:
                        # 如果键值对的名称是“品牌”，则提取其值
                        if kv_item.get("name") == "品牌":
                            product_info["brand"] = kv_item.get(
                                "productsPropertyValue", ""
                            )
                            break  # 找到品牌后即可停止查找

                products.append(product_info)

        return products
    except Exception as e:
        # 记录错误日志
        logger.exception(f"搜索商品失败: {str(e)}")
        # 发生异常时返回空列表
        return []


tool_manager.register_as_function_tool(search_product_by_name)


def search_product_from_es(
    product_name: str,
    specification: Optional[str] = None,
    is_shunluda: bool = False,
    size: int = 20,
) -> List[Dict[str, Any]]:
    """
    直接从Elasticsearch搜索商品，返回按pd_id分组的商品信息列表。
    优先返回有库存的商品组，每个pd_id下包含该商品的所有SKU规格。

    Args:
        product_name: 搜索关键词，通常是商品名称，比如'越南大青芒'或'海南水仙芒'。
        specification: 可选的规格信息，比如'净重20-20.5斤/二级/熟果/赵记传承专用400+'。
        is_shunluda: 是否只搜索顺鹿达商品，默认为False。
        size: 返回结果数量（pd_id组数量），默认为20。

    Returns:
        List[Dict[str, Any]]: 按pd_id分组的商品信息列表，每个pd_id组包含以下字段:
            - pd_id (str): 商品pd_id，作为分组主键。例如: 'PD001'
            - title (str): 商品名称。例如: '海南水仙芒'
            - brand_name (str): 商品品牌。例如: '好果源'
            - category_id (str): 商品分类ID。例如: '123'
            - category_path (str): 商品分类路径。例如: '水果_芒果类'
            - category_type_description (str): 商品分类类型描述。例如: '水果'
            - sku_list (List[Dict]): 该商品的所有SKU列表，每个SKU包含:
                - sku (str): SKU编码。例如: '5440754287'
                - store_quantity (int): 合并后的库存数量（不同仓库库存加总）。例如: 50
                - specification (str): 规格信息。例如: '净重20-20.5斤/二级/熟果'
                - sub_type (str): 子类型描述。例如: '3:鲜沐自营商品'
                - price (float): 按库存加权的平均价格（不同仓库价格按库存量加权平均）。例如: 168.0
    """

    # 初始化类目缓存
    init_category_cache()

    # ES连接配置
    es_host = "es-cn-i7m2pv3ht000o90dy.public.elasticsearch.aliyuncs.com:9200"
    es_index = "summerfarm_item_info"
    es_url = f"http://{es_host}/{es_index}/_search"
    auth = HTTPBasicAuth("elastic", "elastic@Xianmu0619")
    headers = {"Content-Type": "application/json"}

    # 构建查询条件
    must_queries = []

    # 添加商品名称和品牌的查询
    must_queries.append(
        {
            "multi_match": {
                "query": product_name,
                "fields": ["title^1.2", "brandName^1.0"],
                "type": "best_fields",
                "analyzer": "xianmu_ik_max_word",
            }
        }
    )

    # 如果提供了规格信息，添加规格查询
    if specification:
        must_queries.append(
            {
                "multi_match": {
                    "query": specification,
                    "fields": ["specification^0.9"],
                    "type": "best_fields",
                    "analyzer": "xianmu_ik_max_word",
                }
            }
        )

    # 构建过滤条件
    # 以下是各个运营服务大区下，销售额top1的运营服务区。
    target_ids = [
        2750,
        1001,
        14564,
        9585,
        29765,
        24635,
        17006,
        44125,
        44136,
        44179,
        44163,
        39867,
        44158,
    ]
    filter_queries = [
        {"term": {"onSale": 1}},
        {"term": {"deleteFlag": 1}},
        {"term": {"mType": 0}},
        {"term": {"marketItemDeleteFlag": 1}},
        {"terms": {"targetId": target_ids}},
    ]

    # 如果是顺鹿达商品，添加subType过滤
    if is_shunluda:
        filter_queries.append({"term": {"subType": 5}})

    # 构建完整的搜索体
    search_body = {
        "size": size * 2 * len(target_ids),
        "query": {"bool": {"must": must_queries, "filter": filter_queries}},
        "sort": [{"_score": {"order": "desc"}}, {"storeQuantity": {"order": "desc"}}],
        "_source": [
            "title",
            "brandName",
            "itemCode",
            "subType",
            "categoryId",
            "specification",
            "targetId",
            "price",
            "outId", # outId其实就是 inventory.pd_id
            "storeQuantity",
            "warehouseNo",
        ],
    }

    try:
        logger.info(f"ES搜索请求: {json.dumps(search_body, ensure_ascii=False)}")

        # 发送搜索请求
        response = requests.post(
            es_url, auth=auth, headers=headers, data=json.dumps(search_body), timeout=30
        )
        response.raise_for_status()

        # 解析响应
        result = response.json()
        hits = result.get("hits", {}).get("hits", [])

        logger.info(f"ES搜索返回 {len(hits)} 条结果")

        # 用于收集所有商品信息（包含warehouseNo）
        all_products = []

        # 处理搜索结果
        for hit in hits:
            source = hit.get("_source", {})
            item_code = source.get("itemCode", "")

            # 获取分类类型描述
            sub_type = source.get("subType", 1)
            if sub_type == 5:
                sub_type_desc = f"{sub_type}:顺鹿达商品"
            elif sub_type == 3:
                sub_type_desc = f"{sub_type}:鲜沐自营商品"
            elif sub_type in (1, 2):
                sub_type_desc = f"{sub_type}:代销商品"
            else:
                sub_type_desc = f"{sub_type}:代仓商品"

            # 获取分类信息
            category_id = str(source.get("categoryId", 0))
            category_path = category_cache.get(category_id, "其他")

            # 根据mType确定分类类型描述
            cate_type = int(category_type_cache.get(category_id, 1))
            if cate_type == 2:
                category_description = "乳制品"
            elif cate_type == 3:
                category_description = "非乳制品"
            elif cate_type == 4:
                category_description = "水果"
            else:
                category_description = "其他"

            # 构建商品信息，包含warehouseNo
            product_info = {
                "pd_name": source.get("title", ""),
                "weight": source.get("specification", ""),
                "sku": item_code,
                "category_type_description": category_description,
                "category_id": category_id,
                "category_path": category_path,
                "brand": source.get("brandName", ""),
                "sub_type": sub_type_desc,
                "price": source.get("price", 0.0),
                "store_quantity": source.get("storeQuantity", 0),
                "pd_id": source.get("outId", ""),
                "warehouse_no": source.get("warehouseNo", ""),
            }

            all_products.append(product_info)

        # 按itemCode分组，处理不同warehouseNo的库存和价格合并
        merged_products = {}
        for product in all_products:
            item_code = product["sku"]
            
            if item_code not in merged_products:
                # 第一次遇到这个itemCode，直接添加
                merged_products[item_code] = {
                    "pd_name": product["pd_name"],
                    "weight": product["weight"],
                    "sku": product["sku"],
                    "category_type_description": product["category_type_description"],
                    "category_id": product["category_id"],
                    "category_path": product["category_path"],
                    "brand": product["brand"],
                    "sub_type": product["sub_type"],
                    "pd_id": product["pd_id"],
                    "warehouses": [product]  # 存储所有仓库的数据用于合并
                }
            else:
                # 相同itemCode，检查warehouseNo是否不同
                existing_warehouses = merged_products[item_code]["warehouses"]
                existing_warehouse_nos = [w["warehouse_no"] for w in existing_warehouses]
                
                if product["warehouse_no"] not in existing_warehouse_nos:
                    # warehouseNo不同，添加到仓库列表
                    existing_warehouses.append(product)
                # warehouseNo相同则忽略（保留第一个）
        
        # 计算每个itemCode的合并库存和加权平均价格
        final_products = {}
        for item_code, product_data in merged_products.items():
            warehouses = product_data["warehouses"]
            
            # 计算总库存和按库存加权的平均价格
            total_quantity = sum(w["store_quantity"] for w in warehouses)
            
            # 计算加权平均价格：(价格1*库存1 + 价格2*库存2 + ...) / 总库存
            weighted_price_sum = 0.0
            total_weighted_quantity = 0
            
            for w in warehouses:
                if w["price"] > 0 and w["store_quantity"] > 0:
                    weighted_price_sum += w["price"] * w["store_quantity"]
                    total_weighted_quantity += w["store_quantity"]
            
            # 如果有有效的加权数据，计算加权平均价；否则计算简单平均价
            if total_weighted_quantity > 0:
                avg_price = weighted_price_sum / total_weighted_quantity
            else:
                # 回退到简单平均价格（针对库存为0但有价格的情况）
                valid_prices = [w["price"] for w in warehouses if w["price"] > 0]
                avg_price = sum(valid_prices) / len(valid_prices) if valid_prices else 0.0
            
            # 构建最终的商品信息
            final_product = {
                "pd_name": product_data["pd_name"],
                "weight": product_data["weight"],
                "sku": product_data["sku"],
                "category_type_description": product_data["category_type_description"],
                "category_id": product_data["category_id"],
                "category_path": product_data["category_path"],
                "brand": product_data["brand"],
                "sub_type": product_data["sub_type"],
                "price": avg_price,
                "store_quantity": total_quantity,
                "pd_id": product_data["pd_id"],
            }
            
            final_products[item_code] = final_product

        # 按pd_id分组，每个pd_id下包含多个SKU信息
        products_list = list(final_products.values())
        
        # 按pd_id分组
        pd_id_groups = {}
        for product in products_list:
            pd_id = product.get('pd_id', '')
            if not pd_id:
                continue  # 跳过没有pd_id的商品
                
            if pd_id not in pd_id_groups:
                # 初始化pd_id组，保存公共信息
                pd_id_groups[pd_id] = {
                    'pd_id': pd_id,
                    'title': product.get('pd_name', ''),
                    'brand_name': product.get('brand', ''),
                    'category_id': product.get('category_id', ''),
                    'category_path': product.get('category_path', ''),
                    'category_type_description': product.get('category_type_description', ''),
                    'sku_list': []
                }
            
            # 添加SKU信息到该pd_id组
            sku_info = {
                'sku': product.get('sku', ''),
                'store_quantity': product.get('store_quantity', 0),
                'specification': product.get('weight', ''),
                'sub_type': product.get('sub_type', ''),
                'price': product.get('price', 0.0)
            }
            pd_id_groups[pd_id]['sku_list'].append(sku_info)
        
        # 对每个pd_id组内的SKU按库存优先排序
        for pd_id, group_data in pd_id_groups.items():
            sku_list = group_data['sku_list']
            
            # 分离有库存和无库存的SKU
            in_stock_skus = []
            out_of_stock_skus = []
            
            for sku in sku_list:
                if sku['store_quantity'] > 0:
                    in_stock_skus.append(sku)
                else:
                    out_of_stock_skus.append(sku)
            
            # 有库存的SKU按库存倒序排列，无库存的按价格倒序排列
            in_stock_skus.sort(key=lambda x: x['store_quantity'], reverse=True)
            out_of_stock_skus.sort(key=lambda x: x['price'], reverse=True)
            
            # 重新组合SKU列表：有库存的在前，无库存的在后
            group_data['sku_list'] = in_stock_skus + out_of_stock_skus
        
        # 按pd_id组的有库存SKU数量排序，有库存SKU多的pd_id排在前面
        sorted_groups = []
        for pd_id, group_data in pd_id_groups.items():
            in_stock_count = sum(1 for sku in group_data['sku_list'] if sku['store_quantity'] > 0)
            total_stock = sum(sku['store_quantity'] for sku in group_data['sku_list'])
            group_data['_sort_priority'] = (in_stock_count, total_stock)
            sorted_groups.append(group_data)
        
        # 按有库存SKU数量和总库存量排序
        sorted_groups.sort(key=lambda x: x['_sort_priority'], reverse=True)
        
        # 移除临时排序字段并返回前size个pd_id组
        result_groups = []
        for group in sorted_groups[:size]:
            if '_sort_priority' in group:
                del group['_sort_priority']
            result_groups.append(group)
        
        logger.info(f"按pd_id分组，总共 {len(pd_id_groups)} 个pd_id组，返回前 {len(result_groups)} 个")
        
        return result_groups

    except Exception as e:
        logger.exception(f"ES搜索商品失败: {str(e)}")
        raise e


tool_manager.register_as_function_tool(search_product_from_es)
