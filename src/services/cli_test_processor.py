"""
CLI测试处理器 - 不依赖飞书消息ID的查询测试

用于在命令行环境中测试用户查询，无需真实的飞书消息上下文
"""

import asyncio
import time
import uuid
from typing import Dict, Any, Optional

from src.services.agent.api_query_processor import APIQueryProcessor
from src.services.chatbot.history_service import get_conversation_messages, save_user_message
from src.utils.logger import logger


class CliTestProcessor:
    """CLI测试处理器 - 不依赖飞书环境的查询测试"""
    
    async def process_query_directly(
        self, 
        username: str,
        user_query: str,
        user_info: Dict[str, str],
        expected_agent: str = "sales_order_analytics"
    ) -> Dict[str, Any]:
        """
        直接在CLI环境中处理查询，不依赖飞书消息机制
        
        Args:
            username: 用户姓名
            user_query: 查询内容
            user_info: 包含open_id等业务信息
            expected_agent: 预期agent类型
            
        Returns:
            查询结果汇总信息
        """
        
        # 生成会话ID用于追踪
        conversation_id = f"cli_test_{str(uuid.uuid4())[:8]}"
        
        result = {
            "username": username,
            "user_query": user_query,
            "expected_agent": expected_agent,
            "conversation_id": conversation_id,
            "start_time": time.time(),
            "status": "initialized",
            "steps": [],
            "response_content": None,
            "error_message": None,
            "query_duration": 0,
            "tools_used": []
        }
        
        try:
            # 1. 保存用户消息到历史记录
            step1_start = time.time()
            save_success = await asyncio.to_thread(
                save_user_message,
                username=user_info["name"],
                email=user_info["email"],
                conversation_id=conversation_id,
                content=user_query
            )
            
            result["steps"].append({
                "step": "save_user_message",
                "duration": time.time() - step1_start,
                "success": save_success
            })
            
            if not save_success:
                result["error_message"] = "保存用户查询到历史记录失败"
                result["status"] = "failed"
                return result
            
            # 2. 调用coordinator直接处理查询
            step2_start = time.time()
            
            # 构建查询请求
            query_request = {
                "user_query": user_query,
                "user_info": user_info,
                "conversation_id": conversation_id,
                "expected_agent": expected_agent,
                "image_url": None,
                "access_token": None  # CLI模式不需要飞书token
            }
            
            # 使用APIQueryProcessor进行查询
            processor = APIQueryProcessor()
            response_content = ""
            response_count = 0
            
            for response in processor.run_query(
                user_query=user_query,
                user_info=user_info,
                conversation_id=conversation_id
            ):
                if isinstance(response, str):
                    response_content += response
                elif isinstance(response, dict):
                    response_content += str(response)
                response_count += 1
                
            result["query_duration"] = time.time() - step2_start
            result["steps"].append({
                "step": "process_query",
                "duration": result["query_duration"],
                "success": True,
                "response_count": response_count
            })
            
            # 3. 获取最终响应和工具使用情况
            step3_start = time.time()
            try:
                messages = await asyncio.to_thread(
                    get_conversation_messages,
                    conversation_id=conversation_id,
                    username=user_info["name"],
                    email=user_info["email"]
                )
                
                if messages:
                    # 获取最后一条助手回复
                    assistant_messages = [msg for msg in messages if msg.get("role") == "assistant"]
                    if assistant_messages:
                        final_response = assistant_messages[-1].get("content", "")
                        result["response_content"] = final_response
                        
                        # 提取使用的工具
                        for msg in assistant_messages:
                            content = msg.get("content", "")
                            logs = str(msg.get("logs", ""))
                            tools = _extract_tools_from_content(content + " " + logs)
                            result["tools_used"].extend(tools)
                
            except Exception as e:
                logger.warning(f"获取对话历史失败: {e}")
                
            result["steps"].append({
                "step": "retrieve_result",
                "duration": time.time() - step3_start,
                "success": True
            })
            
            result["status"] = "success"
            
        except Exception as e:
            logger.error(f"CLI查询处理失败: {e}", exc_info=True)
            result["error_message"] = str(e)
            result["status"] = "failed"
            
        result["total_duration"] = time.time() - result["start_time"]
        
        return result


def _extract_tools_from_content(content: str) -> list:
    """从响应内容中提取使用的工具"""
    import re
    
    tools = []
    
    # 匹配工具调用模式
    tool_patterns = [
        r"(?:调用|使用|叫做)[\s\u3000]?([^\s\(]+?)[\s\(]",
        r"(?:Named|Called)[\s\u3000]?([^\s\(]+?)[\s\(]",
        r"(?:function|Function)[\s\u3000]+([^\s\(]+?)[\s\(]",
    ]
    
    for pattern in tool_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE | re.UNICODE)
        tools.extend(matches)
    
    return list(set([t.strip() for t in tools if t.strip()]))