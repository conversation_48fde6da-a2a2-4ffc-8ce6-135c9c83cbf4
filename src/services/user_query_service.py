"""
用户查询服务模块

提供通过用户姓名或邮箱查询 open_id 和相关用户信息的功能
"""

from typing import Dict, Any, Optional, List
from src.db.connection import execute_db_query, Database
from src.utils.logger import logger


class UserQueryService:
    """用户查询服务类"""
    
    @staticmethod
    def get_user_info_by_username(username: str) -> Optional[Dict[str, str]]:
        """
        根据用户名从user表获取用户信息
        
        Args:
            username: 用户姓名
            
        Returns:
            Dict包含用户信息，或None表示未找到
        """
        try:
            # 查询用户信息
            sql = """
                SELECT 
                    name, 
                    email, 
                    open_id,
                    avatar,
                    user_id,
                    job_title,
                    first_level_department
                FROM user 
                WHERE name = %s 
                LIMIT 1
            """
            
            result = execute_db_query(sql, params=(username,), fetch='one', database=Database.CHATBI)
            
            if result:
                user_info = {
                    "name": result['name'],
                    "email": result['email'],
                    "open_id": result['open_id'],
                    "avatar_url": result.get('avatar'),
                    "user_id": result.get('user_id'),
                    "job_title": result.get('job_title', '未指定'),
                    "department": result.get('first_level_department', '未指定'),
                    "phone": "未指定"      # 数据库中暂无电话字段
                }
                logger.info(f"✅ 找到用户信息: {user_info['name']} ({user_info['email']})")
                return user_info
            else:
                logger.warning(f"❌ 未找到用户: {username}")
                return None
                
        except Exception as e:
            logger.error(f"获取用户信息失败 {username}: {e}")
            return None
    
    @staticmethod
    def get_user_info_by_email(email: str) -> Optional[Dict[str, str]]:
        """
        根据邮箱地址获取用户信息
        
        Args:
            email: 用户邮箱地址
            
        Returns:
            Dict包含用户信息，或None表示未找到
        """
        try:
            sql = """
                SELECT 
                    name, 
                    email, 
                    open_id,
                    avatar,
                    user_id,
                    job_title,
                    first_level_department
                FROM user 
                WHERE email = %s 
                LIMIT 1
            """
            
            result = execute_db_query(sql, params=(email,), fetch='one', database=Database.CHATBI)
            
            if result:
                user_info = {
                    "name": result['name'],
                    "email": result['email'],
                    "open_id": result['open_id'],
                    "avatar_url": result.get('avatar'),
                    "user_id": result.get('user_id'),
                    "job_title": result.get('job_title', '未指定'),
                    "department": result.get('first_level_department', '未指定'),
                    "phone": "未指定"
                }
                logger.info(f"✅ 找到用户邮箱: {user_info['email']} -> {user_info['name']}")
                return user_info
            else:
                logger.warning(f"❌ 未找到邮箱: {email}")
                return None
                
        except Exception as e:
            logger.error(f"获取用户信息失败 {email}: {e}")
            return None
    
    @staticmethod
    def search_users(search_term: str, limit: int = 5) -> List[Dict[str, str]]:
        """
        模糊搜索用户信息
        
        Args:
            search_term: 搜索关键词（姓名或邮箱）
            limit: 限制返回结果数量
            
        Returns:
            List[Dict[str, str]]: 用户列表
        """
        try:
            sql = """
                SELECT 
                    name, 
                    email, 
                    open_id,
                    avatar,
                    user_id,
                    job_title,
                    first_level_department
                FROM user 
                WHERE name LIKE %s OR email LIKE %s
                LIMIT %s
            """
            
            like_pattern = f"%{search_term}%"
            results = execute_db_query(
                sql, 
                params=(like_pattern, like_pattern, limit), 
                fetch='all', 
                database=Database.CHATBI
            ) or []
            
            users = []
            for result in results:
                users.append({
                    "name": result['name'],
                    "email": result['email'],
                    "open_id": result['open_id'],
                    "avatar_url": result.get('avatar'),
                    "user_id": result.get('user_id'),
                    "job_title": result.get('job_title', '未指定'),
                    "department": result.get('first_level_department', '未指定')
                })
            
            logger.info(f"🔍 搜索 '{search_term}' 找到 {len(users)} 个用户")
            return users
            
        except Exception as e:
            logger.error(f"搜索用户失败: {e}")
            return []
    
    @staticmethod
    def get_open_id_by_user_query(input_str: str) -> Optional[Dict[str, str]]:
        """
        根据输入字符串查询用户信息（支持姓名或邮箱）
        
        Args:
            input_str: 可以是姓名或邮箱地址
            
        Returns:
            Dict包含用户信息，或None表示未找到
        """
        # 首先尝试邮箱格式匹配
        if '@' in input_str:
            user_info = UserQueryService.get_user_info_by_email(input_str)
            if user_info:
                return user_info
        
        # 如果不是邮箱格式，按姓名处理
        return UserQueryService.get_user_info_by_username(input_str)
    
    @staticmethod
    def list_all_users(limit: int = 10) -> List[Dict[str, str]]:
        """
        获取所有用户列表（用于调试）
        
        Args:
            limit: 限制返回数量
            
        Returns:
            List[Dict[str, str]]: 用户列表
        """
        try:
            sql = """
                SELECT 
                    name, 
                    email, 
                    open_id,
                    avatar,
                    user_id,
                    job_title,
                    first_level_department
                FROM user 
                LIMIT %s
            """
            
            results = execute_db_query(sql, params=(limit,), fetch='all', database=Database.CHATBI) or []
            
            users = []
            for result in results:
                users.append({
                    "name": result['name'],
                    "email": result['email'],
                    "open_id": result['open_id'],
                    "avatar_url": result.get('avatar'),
                    "user_id": result.get('user_id'),
                    "job_title": result.get('job_title', '未指定'),
                    "department": result.get('first_level_department', '未指定')
                })
            
            return users
            
        except Exception as e:
            logger.error(f"获取用户列表失败: {e}")
            return []